package com.example.cllcnplatformbackend.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;

/**
 * JWT工具类
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Slf4j
@Component
public class JwtUtil {
    
    @Value("${jwt.secret:cllcn_logistics_platform_secret_key}")
    private String secret;
    
    @Value("${jwt.expiration:86400000}")
    private Long expiration;
    
    /**
     * 生成密钥
     */
    private SecretKey getSecretKey() {
        // 确保密钥长度足够
        String keyString = secret.length() >= 32 ? secret : secret + "0".repeat(32 - secret.length());
        return Keys.hmacShaKeyFor(keyString.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * 生成token
     *
     * @param claims 载荷
     * @return token
     */
    public String generateToken(Map<String, Object> claims) {
        Date now = new Date();
        Date expirationDate = new Date(now.getTime() + expiration);
        
        try {
            return Jwts.builder()
                    .claims(claims)
                    .issuedAt(now)
                    .expiration(expirationDate)
                    .signWith(getSecretKey())
                    .compact();
        } catch (Exception e) {
            log.error("生成JWT token失败", e);
            throw new RuntimeException("生成JWT token失败", e);
        }
    }
    
    /**
     * 解析token
     *
     * @param token token
     * @return 载荷
     */
    public Claims parseToken(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSecretKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            log.error("解析JWT token失败: {}", e.getMessage());
            throw new RuntimeException("解析JWT token失败", e);
        }
    }
    
    /**
     * 验证token是否有效
     *
     * @param token token
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            Claims claims = parseToken(token);
            return !isTokenExpired(claims);
        } catch (Exception e) {
            log.debug("JWT token验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 判断token是否过期
     *
     * @param claims 载荷
     * @return 是否过期
     */
    private boolean isTokenExpired(Claims claims) {
        Date expiration = claims.getExpiration();
        return expiration.before(new Date());
    }
    
    /**
     * 从token中获取用户ID
     *
     * @param token token
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        try {
            Claims claims = parseToken(token);
            Object userId = claims.get("userId");
            if (userId instanceof Integer) {
                return ((Integer) userId).longValue();
            } else if (userId instanceof Long) {
                return (Long) userId;
            } else if (userId instanceof String) {
                return Long.parseLong((String) userId);
            }
            return null;
        } catch (Exception e) {
            log.error("从token中获取用户ID失败", e);
            return null;
        }
    }
    
    /**
     * 从token中获取用户名
     *
     * @param token token
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        try {
            Claims claims = parseToken(token);
            return claims.getSubject();
        } catch (Exception e) {
            log.error("从token中获取用户名失败", e);
            return null;
        }
    }
    
    /**
     * 从token中获取用户角色
     *
     * @param token token
     * @return 用户角色
     */
    public String getRoleFromToken(String token) {
        try {
            Claims claims = parseToken(token);
            return (String) claims.get("role");
        } catch (Exception e) {
            log.error("从token中获取用户角色失败", e);
            return null;
        }
    }
    
    /**
     * 刷新token
     *
     * @param token 原token
     * @return 新token
     */
    public String refreshToken(String token) {
        try {
            Claims claims = parseToken(token);
            // 移除过期时间和签发时间，重新生成
            claims.remove("exp");
            claims.remove("iat");
            return generateToken(claims);
        } catch (Exception e) {
            log.error("刷新JWT token失败", e);
            throw new RuntimeException("刷新JWT token失败", e);
        }
    }
    
    /**
     * 获取token剩余有效时间（毫秒）
     *
     * @param token token
     * @return 剩余有效时间
     */
    public long getTokenRemainingTime(String token) {
        try {
            Claims claims = parseToken(token);
            Date expiration = claims.getExpiration();
            return expiration.getTime() - System.currentTimeMillis();
        } catch (Exception e) {
            log.error("获取token剩余时间失败", e);
            return 0;
        }
    }

    /**
     * 获取token过期时间（秒）
     *
     * @return 过期时间（秒）
     */
    public static Long getExpirationTime() {
        // 默认2小时，可以从配置文件读取
        return 7200L;
    }
}
