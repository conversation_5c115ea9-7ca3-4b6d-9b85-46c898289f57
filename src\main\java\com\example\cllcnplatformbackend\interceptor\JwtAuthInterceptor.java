package com.example.cllcnplatformbackend.interceptor;

import com.example.cllcnplatformbackend.utils.JwtUtil;
import com.example.cllcnplatformbackend.utils.ThreadLocalUtil;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.HashMap;
import java.util.Map;

/**
 * JWT认证拦截器
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthInterceptor implements HandlerInterceptor {
    
    private final JwtUtil jwtUtil;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 预检请求直接放行
        if ("OPTIONS".equals(request.getMethod())) {
            return true;
        }
        
        // 获取请求路径
        String requestURI = request.getRequestURI();
        
        // 白名单路径，无需认证
        if (isWhiteListPath(requestURI)) {
            return true;
        }
        
        // 获取Authorization头
        String authHeader = request.getHeader("Authorization");
        if (!StringUtils.hasText(authHeader) || !authHeader.startsWith("Bearer ")) {
            log.warn("请求路径: {} 缺少Authorization头或格式错误", requestURI);
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"message\":\"未授权访问，请先登录\"}");
            return false;
        }
        
        // 提取token
        String token = authHeader.substring(7);
        
        try {
            // 验证token
            if (!jwtUtil.validateToken(token)) {
                log.warn("请求路径: {} token验证失败", requestURI);
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":401,\"message\":\"token已过期或无效，请重新登录\"}");
                return false;
            }
            
            // 解析token获取用户信息
            Claims claims = jwtUtil.parseToken(token);
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("userId", claims.get("userId"));
            userInfo.put("username", claims.getSubject());
            userInfo.put("role", claims.get("role"));
            userInfo.put("userType", claims.get("userType")); // employee 或 client
            
            // 存储到ThreadLocal
            ThreadLocalUtil.set(userInfo);
            
            log.debug("用户认证成功: userId={}, username={}, role={}", 
                    claims.get("userId"), claims.getSubject(), claims.get("role"));
            
            return true;
            
        } catch (Exception e) {
            log.error("JWT token解析失败: {}", e.getMessage());
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"message\":\"token解析失败，请重新登录\"}");
            return false;
        }
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清理ThreadLocal，防止内存泄漏
        ThreadLocalUtil.remove();
    }
    
    /**
     * 判断是否为白名单路径
     */
    private boolean isWhiteListPath(String path) {
        // 不需要移除context-path前缀，因为已经移除了/api
        
        // 白名单路径
        String[] whiteList = {
                "/auth/login",
                "/auth/register",
                "/auth/refresh",
                "/health",
                "/actuator/**",
                "/swagger-ui/**",
                "/v3/api-docs/**",
                "/swagger-resources/**",
                "/webjars/**",
                "/favicon.ico",
                "/error"
        };
        
        for (String whitePath : whiteList) {
            if (whitePath.endsWith("/**")) {
                String prefix = whitePath.substring(0, whitePath.length() - 3);
                if (path.startsWith(prefix)) {
                    return true;
                }
            } else if (path.equals(whitePath)) {
                return true;
            }
        }
        
        return false;
    }
}
