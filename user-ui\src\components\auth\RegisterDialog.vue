<!--
  注册弹窗组件
  
  @description 用户注册弹窗，支持手机号/邮箱注册
  <AUTHOR>
  @date 2025-07-18 16:00:00 +08:00
  @reference 基于shadcn-vue设计系统
-->

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Checkbox } from '@/components/ui/checkbox'
import { Eye, EyeOff, Phone, Mail, Lock, UserPlus, User } from 'lucide-vue-next'
import { useUserStore } from '@/stores/user'
import { useUIStore } from '@/stores/ui'

// Store和Router
const userStore = useUserStore()
const uiStore = useUIStore()
const router = useRouter()

// Props和Emits
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:open', 'switch-to-login', 'register-success'])

// 响应式状态
const isOpen = ref(props.open)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const registerType = ref('phone') // 'phone' | 'email'
const loading = ref(false)

// 表单数据
const registerForm = ref({
  phone: '',
  email: '',
  password: '',
  confirmPassword: '',
  captcha: '',
  agreeTerms: false
})

// 验证码
const captchaCode = ref('')
const captchaImage = ref('')

// 表单验证
const errors = ref({
  phone: '',
  email: '',
  password: '',
  confirmPassword: '',
  captcha: '',
  agreeTerms: ''
})

// 监听props变化
watch(() => props.open, (newVal) => {
  isOpen.value = newVal
})

// 监听内部状态变化
watch(isOpen, (newVal) => {
  emit('update:open', newVal)
  if (newVal) {
    generateCaptcha()
  }
})

// 生成验证码
const generateCaptcha = () => {
  // 生成4位数字字母混合验证码
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  captchaCode.value = result

  // 生成验证码图片（简单的canvas绘制）
  const canvas = document.createElement('canvas')
  canvas.width = 120
  canvas.height = 40
  const ctx = canvas.getContext('2d')

  // 背景
  ctx.fillStyle = '#f8f9fa'
  ctx.fillRect(0, 0, 120, 40)

  // 干扰线
  for (let i = 0; i < 3; i++) {
    ctx.strokeStyle = `hsl(${Math.random() * 360}, 50%, 70%)`
    ctx.beginPath()
    ctx.moveTo(Math.random() * 120, Math.random() * 40)
    ctx.lineTo(Math.random() * 120, Math.random() * 40)
    ctx.stroke()
  }

  // 验证码文字
  ctx.font = '20px Arial'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'

  for (let i = 0; i < result.length; i++) {
    ctx.fillStyle = `hsl(${Math.random() * 360}, 70%, 40%)`
    ctx.save()
    ctx.translate(20 + i * 20, 20)
    ctx.rotate((Math.random() - 0.5) * 0.5)
    ctx.fillText(result[i], 0, 0)
    ctx.restore()
  }

  captchaImage.value = canvas.toDataURL()
}

// 切换密码显示
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

const toggleConfirmPasswordVisibility = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

// 表单验证
const validateForm = () => {
  errors.value = { phone: '', email: '', password: '', confirmPassword: '', captcha: '', agreeTerms: '' }
  let isValid = true

  // 手机号/邮箱验证
  if (registerType.value === 'phone') {
    if (!registerForm.value.phone) {
      errors.value.phone = '请输入手机号'
      isValid = false
    } else if (!/^1[3-9]\d{9}$/.test(registerForm.value.phone)) {
      errors.value.phone = '请输入正确的手机号'
      isValid = false
    }
  } else {
    if (!registerForm.value.email) {
      errors.value.email = '请输入邮箱'
      isValid = false
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(registerForm.value.email)) {
      errors.value.email = '请输入正确的邮箱格式'
      isValid = false
    }
  }

  // 密码验证
  if (!registerForm.value.password) {
    errors.value.password = '请输入密码'
    isValid = false
  } else if (registerForm.value.password.length < 6) {
    errors.value.password = '密码至少6位'
    isValid = false
  }

  // 确认密码验证
  if (!registerForm.value.confirmPassword) {
    errors.value.confirmPassword = '请确认密码'
    isValid = false
  } else if (registerForm.value.password !== registerForm.value.confirmPassword) {
    errors.value.confirmPassword = '两次密码输入不一致'
    isValid = false
  }

  // 验证码验证
  if (!registerForm.value.captcha) {
    errors.value.captcha = '请输入验证码'
    isValid = false
  } else if (registerForm.value.captcha.toLowerCase() !== captchaCode.value.toLowerCase()) {
    errors.value.captcha = '验证码错误'
    isValid = false
  }

  // 协议验证
  if (!registerForm.value.agreeTerms) {
    errors.value.agreeTerms = '请同意服务条款和隐私政策'
    isValid = false
  }

  return isValid
}

// 处理注册
const handleRegister = async () => {
  if (!validateForm()) return

  loading.value = true
  try {
    // 模拟注册API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 模拟注册成功
    const user = {
      id: Date.now(),
      name: '用户' + Date.now().toString().slice(-4),
      email: registerForm.value.email || `${registerForm.value.phone}@example.com`,
      phone: registerForm.value.phone || '13800138000',
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=user${Date.now()}`
    }

    emit('register-success', user)
    isOpen.value = false
    
    // 重置表单
    resetForm()
  } catch (error) {
    console.error('注册失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  registerForm.value = {
    phone: '',
    email: '',
    password: '',
    confirmPassword: '',
    captcha: '',
    agreeTerms: false
  }
  errors.value = { phone: '', email: '', password: '', confirmPassword: '', captcha: '', agreeTerms: '' }
  showPassword.value = false
  showConfirmPassword.value = false
  generateCaptcha()
}

// 切换到登录
const switchToLogin = () => {
  isOpen.value = false
  emit('switch-to-login')
}

// 关闭弹窗时重置表单
const handleClose = () => {
  resetForm()
}
</script>

<template>
  <Dialog v-model:open="isOpen" @update:open="handleClose">
    <DialogContent class="sm:max-w-md max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle class="text-center text-xl font-bold">注册账户</DialogTitle>
        <DialogDescription class="text-center">
          创建您的账户，开始使用我们的服务
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4">
        <!-- 注册方式切换 -->
        <Tabs v-model="registerType" class="w-full">
          <TabsList class="grid w-full grid-cols-2">
            <TabsTrigger value="phone" class="flex items-center gap-2">
              <Phone class="h-4 w-4" />
              手机号
            </TabsTrigger>
            <TabsTrigger value="email" class="flex items-center gap-2">
              <Mail class="h-4 w-4" />
              邮箱
            </TabsTrigger>
          </TabsList>

          <!-- 手机号注册 -->
          <TabsContent value="phone" class="space-y-4">
            <div class="space-y-2">
              <Label for="register-phone">手机号</Label>
              <Input
                id="register-phone"
                v-model="registerForm.phone"
                type="tel"
                placeholder="请输入手机号"
                :class="{ 'border-red-500': errors.phone }"
              />
              <p v-if="errors.phone" class="text-sm text-red-500">{{ errors.phone }}</p>
            </div>
          </TabsContent>

          <!-- 邮箱注册 -->
          <TabsContent value="email" class="space-y-4">
            <div class="space-y-2">
              <Label for="register-email">邮箱</Label>
              <Input
                id="register-email"
                v-model="registerForm.email"
                type="email"
                placeholder="请输入邮箱"
                :class="{ 'border-red-500': errors.email }"
              />
              <p v-if="errors.email" class="text-sm text-red-500">{{ errors.email }}</p>
            </div>
          </TabsContent>
        </Tabs>

        <!-- 密码输入 -->
        <div class="space-y-2">
          <Label for="register-password">密码</Label>
          <div class="relative">
            <Input
              id="register-password"
              v-model="registerForm.password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入密码（至少6位）"
              :class="{ 'border-red-500': errors.password }"
              class="pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              class="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              @click="togglePasswordVisibility"
            >
              <Eye v-if="showPassword" class="h-4 w-4" />
              <EyeOff v-else class="h-4 w-4" />
            </Button>
          </div>
          <p v-if="errors.password" class="text-sm text-red-500">{{ errors.password }}</p>
        </div>

        <!-- 确认密码输入 -->
        <div class="space-y-2">
          <Label for="register-confirmPassword">确认密码</Label>
          <div class="relative">
            <Input
              id="register-confirmPassword"
              v-model="registerForm.confirmPassword"
              :type="showConfirmPassword ? 'text' : 'password'"
              placeholder="请再次输入密码"
              :class="{ 'border-red-500': errors.confirmPassword }"
              class="pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              class="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              @click="toggleConfirmPasswordVisibility"
            >
              <Eye v-if="showConfirmPassword" class="h-4 w-4" />
              <EyeOff v-else class="h-4 w-4" />
            </Button>
          </div>
          <p v-if="errors.confirmPassword" class="text-sm text-red-500">{{ errors.confirmPassword }}</p>
        </div>

        <!-- 验证码输入 -->
        <div class="space-y-2">
          <Label for="register-captcha">验证码</Label>
          <div class="flex space-x-2">
            <Input
              id="register-captcha"
              v-model="registerForm.captcha"
              type="text"
              placeholder="请输入验证码"
              :class="{ 'border-red-500': errors.captcha }"
              class="flex-1"
              maxlength="4"
            />
            <div
              class="w-[120px] h-10 border rounded-md cursor-pointer flex items-center justify-center bg-muted hover:bg-muted/80 transition-colors"
              @click="generateCaptcha"
              title="点击刷新验证码"
            >
              <img
                v-if="captchaImage"
                :src="captchaImage"
                alt="验证码"
                class="w-full h-full object-contain rounded"
              />
            </div>
          </div>
          <p v-if="errors.captcha" class="text-sm text-red-500">{{ errors.captcha }}</p>
        </div>

        <!-- 服务条款 -->
        <div class="space-y-2">
          <div class="flex items-start space-x-2">
            <Checkbox
              id="register-agreeTerms"
              v-model:checked="registerForm.agreeTerms"
              :class="{ 'border-red-500': errors.agreeTerms }"
            />
            <Label for="register-agreeTerms" class="text-sm leading-relaxed">
              我已阅读并同意
              <Button variant="link" class="px-1 h-auto text-sm">服务条款</Button>
              和
              <Button variant="link" class="px-1 h-auto text-sm">隐私政策</Button>
            </Label>
          </div>
          <p v-if="errors.agreeTerms" class="text-sm text-red-500">{{ errors.agreeTerms }}</p>
        </div>

        <!-- 注册按钮 -->
        <Button 
          @click="handleRegister" 
          class="w-full" 
          :disabled="loading"
        >
          <UserPlus v-if="!loading" class="mr-2 h-4 w-4" />
          {{ loading ? '注册中...' : '注册' }}
        </Button>

        <!-- 登录链接 -->
        <div class="text-center text-sm">
          <span class="text-muted-foreground">已有账户？</span>
          <Button variant="link" class="px-1" @click="switchToLogin">
            立即登录
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>
