# 开发环境配置
server:
  port: 8081

spring:
  # 数据源配置 - 混合架构（用户认证共享，业务数据独立）
  datasource:
    # 公司管理系统数据库（用户认证共享）
    company:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ********************************************************************************************************************************************************************
      username: ${DB_COMPANY_USERNAME:root}
      password: ${DB_COMPANY_PASSWORD:123456}
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000

    # 物流平台数据库（业务数据独立）
    logistics:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *************************************************************************************************************************************************************
      username: ${DB_LOGISTICS_USERNAME:root}
      password: ${DB_LOGISTICS_PASSWORD:123456}
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate  # 开发环境使用validate，不自动创建表
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
  
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 5000ms

  # RabbitMQ配置（预留，后续可启用消息队列功能）
  # rabbitmq:
  #   host: ${RABBITMQ_HOST:localhost}
  #   port: ${RABBITMQ_PORT:5672}
  #   username: ${RABBITMQ_USERNAME:guest}
  #   password: ${RABBITMQ_PASSWORD:guest}
  #   virtual-host: ${RABBITMQ_VHOST:/}
  #   listener:
  #     simple:
  #       acknowledge-mode: manual
  #       retry:
  #         enabled: true
  #         max-attempts: 3
  
  # SQL初始化
  sql:
    init:
      mode: never  # 开发环境不自动执行SQL脚本

# 文件上传配置
app:
  upload:
    path: D:/Jobs/Project/cllcn_logistics_platform/uploads  # 开发环境使用绝对路径
    base-url: http://localhost:8081  # 开发环境后端地址

# JWT配置
jwt:
  secret: cllcn_logistics_platform_dev_secret_key_2025
  expiration: 86400000  # 24小时

# 日志配置
logging:
  level:
    com.example.cllcnplatformbackend: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  file:
    name: logs/cllcn-logistics-dev.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30

# SpringDoc配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    operations-sorter: method
    tags-sorter: alpha
  show-actuator: true

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 开发环境暴露所有端点
  endpoint:
    health:
      show-details: always
