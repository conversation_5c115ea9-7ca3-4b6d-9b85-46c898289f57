package com.example.cllcnplatformbackend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 用户登录请求DTO
 * 支持邮箱或手机号登录
 * 
 * <AUTHOR> Platform
 * @since 2025-07-31
 */
@Data
@Schema(description = "用户登录请求")
public class LoginRequest {

    @NotBlank(message = "登录账号不能为空")
    @Schema(description = "登录账号（邮箱或手机号）", example = "<EMAIL>")
    private String account;

    @NotBlank(message = "密码不能为空")
    @Schema(description = "登录密码", example = "cllcn123")
    private String password;

    @Schema(description = "记住我", example = "false")
    private Boolean rememberMe = false;
}
