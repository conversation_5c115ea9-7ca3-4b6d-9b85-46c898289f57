package com.example.cllcnplatformbackend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户登录响应DTO
 * 
 * <AUTHOR> Platform
 * @since 2025-07-31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户登录响应")
public class LoginResponse {

    @Schema(description = "JWT访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String accessToken;

    @Schema(description = "令牌类型", example = "Bearer")
    private String tokenType = "Bearer";

    @Schema(description = "令牌过期时间（秒）", example = "7200")
    private Long expiresIn;

    @Schema(description = "用户信息")
    private UserInfo userInfo;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "用户基本信息")
    public static class UserInfo {
        
        @Schema(description = "平台用户ID", example = "C123")
        private String platformUserId;
        
        @Schema(description = "用户类型", example = "client")
        private String userType;
        
        @Schema(description = "真实姓名", example = "张三")
        private String realName;
        
        @Schema(description = "邮箱", example = "<EMAIL>")
        private String email;
        
        @Schema(description = "手机号", example = "13800138000")
        private String mobile;
        
        @Schema(description = "状态", example = "active")
        private String status;
    }

    /**
     * 创建登录成功响应
     */
    public static LoginResponse success(String accessToken, Long expiresIn, UserInfo userInfo) {
        return new LoginResponse(accessToken, "Bearer", expiresIn, userInfo);
    }
}
