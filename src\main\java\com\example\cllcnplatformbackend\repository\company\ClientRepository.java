package com.example.cllcnplatformbackend.repository.company;

import com.example.cllcnplatformbackend.entity.company.Client;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 客户Repository（公司管理系统）
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Repository
public interface ClientRepository extends JpaRepository<Client, Long> {
    
    /**
     * 根据邮箱查找客户
     */
    Optional<Client> findByEmail(String email);
    
    /**
     * 根据手机号查找客户
     */
    Optional<Client> findByPhone(String phone);
    
    /**
     * 根据邮箱查找激活状态的客户（用于登录）
     */
    @Query("SELECT c FROM Client c WHERE c.email = :email AND c.status IN ('审核通过', '已合作') AND c.password IS NOT NULL")
    Optional<Client> findActiveByEmail(@Param("email") String email);
    
    /**
     * 根据手机号查找激活状态的客户（用于登录）
     */
    @Query("SELECT c FROM Client c WHERE c.phone = :phone AND c.status IN ('审核通过', '已合作') AND c.password IS NOT NULL")
    Optional<Client> findActiveByPhone(@Param("phone") String phone);
    
    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);
    
    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);
    
    /**
     * 根据公司名称查找客户
     */
    java.util.List<Client> findByCompanyNameContaining(String companyName);
    
    /**
     * 查找所有激活状态的客户
     */
    @Query("SELECT c FROM Client c WHERE c.status IN ('审核通过', '已合作') ORDER BY c.createTime DESC")
    java.util.List<Client> findAllActive();
    
    /**
     * 根据状态查找客户
     */
    java.util.List<Client> findByStatus(String status);

    /**
     * 根据邮箱和状态列表查找客户（用于认证）
     */
    Client findByEmailAndStatusIn(String email, java.util.List<String> statusList);

    /**
     * 根据手机号和状态列表查找客户（用于认证）
     */
    Client findByPhoneAndStatusIn(String phone, java.util.List<String> statusList);

    /**
     * 根据客户ID和状态列表查找客户（用于认证）
     */
    Client findByClientIdAndStatusIn(Integer clientId, java.util.List<String> statusList);
}
