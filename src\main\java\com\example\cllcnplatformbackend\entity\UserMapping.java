package com.example.cllcnplatformbackend.entity.logistics;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 用户映射实体类（物流平台专用）
 * 可选：用于缓存公司管理系统的用户信息，提高查询性能
 * 在混合架构中，这个表是可选的，主要用于性能优化
 *
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "user_mapping", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"original_user_id", "user_type"}))
public class UserMapping {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 原始用户ID（来自公司管理系统）
     */
    @Column(name = "original_user_id", nullable = false)
    private Long originalUserId;
    
    /**
     * 用户类型：employee（员工）或 client（客户）
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "user_type", nullable = false, length = 20)
    private UserType userType;
    
    /**
     * 用户名
     */
    @Column(name = "username", nullable = false, length = 50)
    private String username;
    
    /**
     * 邮箱
     */
    @Column(name = "email", length = 100)
    private String email;
    
    /**
     * 手机号
     */
    @Column(name = "phone", length = 20)
    private String phone;
    
    /**
     * 姓名
     */
    @Column(name = "name", nullable = false, length = 50)
    private String name;
    
    /**
     * 状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20)
    private UserStatus status = UserStatus.ACTIVE;
    
    /**
     * 角色
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "role", length = 20)
    private UserRole role = UserRole.USER;
    
    /**
     * 部门ID（仅员工有效）
     */
    @Column(name = "department_id")
    private Long departmentId;
    
    /**
     * 职位ID（仅员工有效）
     */
    @Column(name = "position_id")
    private Long positionId;
    
    /**
     * 公司ID（仅客户有效）
     */
    @Column(name = "company_id")
    private Long companyId;
    
    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 用户类型枚举
     */
    public enum UserType {
        EMPLOYEE,  // 员工
        CLIENT     // 客户
    }
    
    /**
     * 用户状态枚举
     */
    public enum UserStatus {
        ACTIVE,    // 激活
        INACTIVE,  // 未激活
        LOCKED,    // 锁定
        DELETED    // 已删除
    }
    
    /**
     * 用户角色枚举
     */
    public enum UserRole {
        ADMIN,     // 管理员
        USER,      // 普通用户
        MANAGER,   // 经理
        OPERATOR   // 操作员
    }
    
    /**
     * 判断是否为员工
     */
    public boolean isEmployee() {
        return UserType.EMPLOYEE.equals(this.userType);
    }
    
    /**
     * 判断是否为客户
     */
    public boolean isClient() {
        return UserType.CLIENT.equals(this.userType);
    }
    
    /**
     * 判断是否为激活状态
     */
    public boolean isActive() {
        return UserStatus.ACTIVE.equals(this.status);
    }
    
    /**
     * 判断是否为管理员
     */
    public boolean isAdmin() {
        return UserRole.ADMIN.equals(this.role);
    }
}
