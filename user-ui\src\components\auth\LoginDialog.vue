<!--
  登录弹窗组件
  
  @description 用户登录弹窗，支持手机号/邮箱登录
  <AUTHOR>
  @date 2025-07-18 16:00:00 +08:00
  @reference 基于shadcn-vue设计系统
-->

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Checkbox } from '@/components/ui/checkbox'
import { Eye, EyeOff, Phone, Mail, Lock, User } from 'lucide-vue-next'
import { useUserStore } from '@/stores/user'
import { useUIStore } from '@/stores/ui'

// Store和Router
const userStore = useUserStore()
const uiStore = useUIStore()
const router = useRouter()

// Props和Emits定义
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:open', 'switch-to-register', 'login-success'])

// 响应式状态
const isOpen = ref(props.open)
const showPassword = ref(false)
const loginType = ref('phone') // 'phone' | 'email'
const loading = ref(false)

// 表单数据
const loginForm = ref({
  phone: '',
  email: '',
  password: '',
  captcha: '',
  rememberMe: false
})

// 验证码
const captchaCode = ref('')
const captchaImage = ref('')

// 表单验证
const errors = ref({
  phone: '',
  email: '',
  password: '',
  captcha: ''
})

// 监听props变化
watch(() => props.open, (newVal) => {
  isOpen.value = newVal
})

// 监听内部状态变化
watch(isOpen, (newVal) => {
  emit('update:open', newVal)
  if (newVal) {
    generateCaptcha()
  }
})

// 生成验证码
const generateCaptcha = () => {
  // 生成4位数字字母混合验证码
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  captchaCode.value = result

  // 生成验证码图片（简单的canvas绘制）
  const canvas = document.createElement('canvas')
  canvas.width = 120
  canvas.height = 40
  const ctx = canvas.getContext('2d')

  // 背景
  ctx.fillStyle = '#f8f9fa'
  ctx.fillRect(0, 0, 120, 40)

  // 干扰线
  for (let i = 0; i < 3; i++) {
    ctx.strokeStyle = `hsl(${Math.random() * 360}, 50%, 70%)`
    ctx.beginPath()
    ctx.moveTo(Math.random() * 120, Math.random() * 40)
    ctx.lineTo(Math.random() * 120, Math.random() * 40)
    ctx.stroke()
  }

  // 验证码文字
  ctx.font = '20px Arial'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'

  for (let i = 0; i < result.length; i++) {
    ctx.fillStyle = `hsl(${Math.random() * 360}, 70%, 40%)`
    ctx.save()
    ctx.translate(20 + i * 20, 20)
    ctx.rotate((Math.random() - 0.5) * 0.5)
    ctx.fillText(result[i], 0, 0)
    ctx.restore()
  }

  captchaImage.value = canvas.toDataURL()
}

// 切换密码显示
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 表单验证
const validateForm = () => {
  errors.value = { phone: '', email: '', password: '', captcha: '' }
  let isValid = true

  if (loginType.value === 'phone') {
    if (!loginForm.value.phone) {
      errors.value.phone = '请输入手机号'
      isValid = false
    } else if (!/^1[3-9]\d{9}$/.test(loginForm.value.phone)) {
      errors.value.phone = '请输入正确的手机号'
      isValid = false
    }
  } else {
    if (!loginForm.value.email) {
      errors.value.email = '请输入邮箱'
      isValid = false
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(loginForm.value.email)) {
      errors.value.email = '请输入正确的邮箱格式'
      isValid = false
    }
  }

  if (!loginForm.value.password) {
    errors.value.password = '请输入密码'
    isValid = false
  } else if (loginForm.value.password.length < 6) {
    errors.value.password = '密码至少6位'
    isValid = false
  }

  if (!loginForm.value.captcha) {
    errors.value.captcha = '请输入验证码'
    isValid = false
  } else if (loginForm.value.captcha.toLowerCase() !== captchaCode.value.toLowerCase()) {
    errors.value.captcha = '验证码错误'
    isValid = false
  }

  return isValid
}

// 处理登录
const handleLogin = async () => {
  if (!validateForm()) return

  loading.value = true
  try {
    // 准备登录凭据
    const credentials = {
      username: loginType.value === 'phone' ? loginForm.value.phone : loginForm.value.email,
      password: loginForm.value.password,
      rememberMe: loginForm.value.rememberMe
    }

    // 调用用户store的登录方法
    await userStore.login(credentials)

    // 登录成功，关闭弹窗
    isOpen.value = false

    // 跳转到重定向路径
    const redirectPath = uiStore.loginRedirectPath || '/'
    if (redirectPath !== router.currentRoute.value.path) {
      await router.push(redirectPath)
    }

    // 重置表单
    resetForm()

    emit('login-success')
  } catch (error) {
    console.error('登录失败:', error)
    // 错误信息已经在store中通过toast显示了
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  loginForm.value = {
    phone: '',
    email: '',
    password: '',
    captcha: '',
    rememberMe: false
  }
  errors.value = { phone: '', email: '', password: '', captcha: '' }
  showPassword.value = false
  generateCaptcha()
}

// 切换到注册
const switchToRegister = () => {
  isOpen.value = false
  emit('switch-to-register')
}

// 关闭弹窗时重置表单
const handleClose = () => {
  resetForm()
}
</script>

<template>
  <Dialog v-model:open="isOpen" @update:open="handleClose">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle class="text-center text-xl font-bold">登录账户</DialogTitle>
        <DialogDescription class="text-center">
          欢迎回来，请登录您的账户
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- 登录方式切换 -->
        <Tabs v-model="loginType" class="w-full">
          <TabsList class="grid w-full grid-cols-2">
            <TabsTrigger value="phone" class="flex items-center gap-2">
              <Phone class="h-4 w-4" />
              手机号
            </TabsTrigger>
            <TabsTrigger value="email" class="flex items-center gap-2">
              <Mail class="h-4 w-4" />
              邮箱
            </TabsTrigger>
          </TabsList>

          <!-- 手机号登录 -->
          <TabsContent value="phone" class="space-y-4">
            <div class="space-y-2">
              <Label for="login-phone">手机号</Label>
              <Input
                id="login-phone"
                v-model="loginForm.phone"
                type="tel"
                placeholder="请输入手机号"
                :class="{ 'border-red-500': errors.phone }"
              />
              <p v-if="errors.phone" class="text-sm text-red-500">{{ errors.phone }}</p>
            </div>
          </TabsContent>

          <!-- 邮箱登录 -->
          <TabsContent value="email" class="space-y-4">
            <div class="space-y-2">
              <Label for="login-email">邮箱</Label>
              <Input
                id="login-email"
                v-model="loginForm.email"
                type="email"
                placeholder="请输入邮箱"
                :class="{ 'border-red-500': errors.email }"
              />
              <p v-if="errors.email" class="text-sm text-red-500">{{ errors.email }}</p>
            </div>
          </TabsContent>
        </Tabs>

        <!-- 密码输入 -->
        <div class="space-y-2">
          <Label for="login-password">密码</Label>
          <div class="relative">
            <Input
              id="login-password"
              v-model="loginForm.password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入密码"
              :class="{ 'border-red-500': errors.password }"
              class="pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              class="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              @click="togglePasswordVisibility"
            >
              <Eye v-if="showPassword" class="h-4 w-4" />
              <EyeOff v-else class="h-4 w-4" />
            </Button>
          </div>
          <p v-if="errors.password" class="text-sm text-red-500">{{ errors.password }}</p>
        </div>

        <!-- 验证码输入 -->
        <div class="space-y-2">
          <Label for="login-captcha">验证码</Label>
          <div class="flex space-x-2">
            <Input
              id="login-captcha"
              v-model="loginForm.captcha"
              type="text"
              placeholder="请输入验证码"
              :class="{ 'border-red-500': errors.captcha }"
              class="flex-1"
              maxlength="4"
            />
            <div
              class="w-[120px] h-10 border rounded-md cursor-pointer flex items-center justify-center bg-muted hover:bg-muted/80 transition-colors"
              @click="generateCaptcha"
              title="点击刷新验证码"
            >
              <img
                v-if="captchaImage"
                :src="captchaImage"
                alt="验证码"
                class="w-full h-full object-contain rounded"
              />
            </div>
          </div>
          <p v-if="errors.captcha" class="text-sm text-red-500">{{ errors.captcha }}</p>
        </div>

        <!-- 记住我和忘记密码 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Checkbox id="login-remember" v-model:checked="loginForm.rememberMe" />
            <Label for="login-remember" class="text-sm">记住我</Label>
          </div>
          <Button variant="link" class="px-0 text-sm">
            忘记密码？
          </Button>
        </div>

        <!-- 登录按钮 -->
        <Button 
          @click="handleLogin" 
          class="w-full" 
          :disabled="loading"
        >
          <User v-if="!loading" class="mr-2 h-4 w-4" />
          {{ loading ? '登录中...' : '登录' }}
        </Button>

        <!-- 注册链接 -->
        <div class="text-center text-sm">
          <span class="text-muted-foreground">还没有账户？</span>
          <Button variant="link" class="px-1" @click="switchToRegister">
            立即注册
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>
