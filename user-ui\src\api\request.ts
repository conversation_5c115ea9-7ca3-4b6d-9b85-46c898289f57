import axios from 'axios'
import { toast } from 'vue-sonner'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081',
  timeout: 10000
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 从localStorage获取token
    const token = localStorage.getItem('user_token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const res = response.data
    
    // 如果返回的状态码不是200，说明接口有问题
    if (res.code !== 200) {
      // 401: 未登录或token过期
      if (res.code === 401) {
        localStorage.removeItem('user_token')
        localStorage.removeItem('user_refresh_token')
        toast.error('登录已过期，请重新登录')
        router.push('/login')
        return Promise.reject(new Error(res.message || '登录已过期'))
      }
      
      toast.error(res.message || '系统错误')
      return Promise.reject(new Error(res.message || '系统错误'))
    } else {
      return res
    }
  },
  (error) => {
    // 处理401错误
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('user_token')
      localStorage.removeItem('user_refresh_token')
      toast.error('登录已过期，请重新登录')
      router.push('/login')
      return Promise.reject(new Error('登录已过期'))
    }
    
    // 显示错误信息
    let errorMessage = '系统错误'
    if (error.response) {
      errorMessage = error.response.data?.message || `请求失败 (${error.response.status})`
    } else if (error.request) {
      errorMessage = '无法连接到服务器，请检查网络'
    }
    
    toast.error(errorMessage)
    return Promise.reject(error)
  }
)

export default service
