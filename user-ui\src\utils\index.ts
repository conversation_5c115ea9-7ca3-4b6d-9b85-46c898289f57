/**
 * 通用工具函数
 *
 * @description 提供项目中常用的工具函数
 * <AUTHOR>
 * @date 2025-07-17 10:04:58 +08:00
 * @reference 基于 project_document/architecture/code_architecture_final.md
 * @note 与admin-ui共享相同架构，但独立实现
 */

import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

/**
 * CSS类名合并工具函数
 * 用于shadcn-vue组件的样式合并
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 格式化相关工具
 */
export const format = {
  /**
   * 格式化货币
   */
  currency: (amount: number, currency = 'CNY'): string => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency
    }).format(amount)
  },

  /**
   * 格式化日期
   */
  date: (date: Date | string, format = 'YYYY-MM-DD HH:mm:ss'): string => {
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  },

  /**
   * 格式化相对时间
   */
  relativeTime: (date: Date | string): string => {
    const now = new Date()
    const target = new Date(date)
    const diff = now.getTime() - target.getTime()

    const minute = 60 * 1000
    const hour = 60 * minute
    const day = 24 * hour
    const week = 7 * day
    const month = 30 * day

    if (diff < minute) {
      return '刚刚'
    } else if (diff < hour) {
      return `${Math.floor(diff / minute)}分钟前`
    } else if (diff < day) {
      return `${Math.floor(diff / hour)}小时前`
    } else if (diff < week) {
      return `${Math.floor(diff / day)}天前`
    } else if (diff < month) {
      return `${Math.floor(diff / week)}周前`
    } else {
      return format.date(date, 'YYYY-MM-DD')
    }
  },

  /**
   * 格式化文件大小
   */
  fileSize: (bytes: number): string => {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  /**
   * 格式化数字
   */
  number: (num: number, decimals = 2): string => {
    return num.toLocaleString('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    })
  },

  /**
   * 格式化手机号（隐藏中间4位）
   */
  phone: (phone: string): string => {
    if (!phone || phone.length !== 11) return phone
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }
}

/**
 * 验证相关工具
 */
export const validate = {
  /**
   * 验证邮箱
   */
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  /**
   * 验证手机号
   */
  phone: (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },

  /**
   * 验证身份证号
   */
  idCard: (idCard: string): boolean => {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return idCardRegex.test(idCard)
  },

  /**
   * 验证URL
   */
  url: (url: string): boolean => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },

  /**
   * 验证密码强度
   */
  password: (password: string): {
    valid: boolean
    strength: 'weak' | 'medium' | 'strong'
    message: string
  } => {
    if (password.length < 6) {
      return { valid: false, strength: 'weak', message: '密码长度至少6位' }
    }

    let score = 0
    if (/[a-z]/.test(password)) score++
    if (/[A-Z]/.test(password)) score++
    if (/\d/.test(password)) score++
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++

    if (score < 2) {
      return { valid: false, strength: 'weak', message: '密码强度太弱' }
    } else if (score < 3) {
      return { valid: true, strength: 'medium', message: '密码强度中等' }
    } else {
      return { valid: true, strength: 'strong', message: '密码强度很强' }
    }
  }
}

/**
 * 存储相关工具
 */
export const storage = {
  /**
   * 设置localStorage
   */
  setLocal: (key: string, value: any): void => {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('localStorage设置失败:', error)
    }
  },

  /**
   * 获取localStorage
   */
  getLocal: <T = any>(key: string): T | null => {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.error('localStorage获取失败:', error)
      return null
    }
  },

  /**
   * 移除localStorage
   */
  removeLocal: (key: string): void => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('localStorage移除失败:', error)
    }
  },

  /**
   * 设置sessionStorage
   */
  setSession: (key: string, value: any): void => {
    try {
      sessionStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('sessionStorage设置失败:', error)
    }
  },

  /**
   * 获取sessionStorage
   */
  getSession: <T = any>(key: string): T | null => {
    try {
      const item = sessionStorage.getItem(key)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.error('sessionStorage获取失败:', error)
      return null
    }
  },

  /**
   * 移除sessionStorage
   */
  removeSession: (key: string): void => {
    try {
      sessionStorage.removeItem(key)
    } catch (error) {
      console.error('sessionStorage移除失败:', error)
    }
  }
}

/**
 * 通用工具函数
 */
export const utils = {
  /**
   * 防抖函数
   */
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout
    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func.apply(null, args), wait)
    }
  },

  /**
   * 节流函数
   */
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(null, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },

  /**
   * 深拷贝
   */
  deepClone: <T>(obj: T): T => {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime()) as any
    if (obj instanceof Array) return obj.map(item => utils.deepClone(item)) as any
    if (typeof obj === 'object') {
      const clonedObj = {} as any
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = utils.deepClone(obj[key])
        }
      }
      return clonedObj
    }
    return obj
  },

  /**
   * 生成UUID
   */
  generateUUID: (): string => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  },

  /**
   * 下载文件
   */
  downloadFile: (url: string, filename?: string): void => {
    const link = document.createElement('a')
    link.href = url
    if (filename) {
      link.download = filename
    }
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  },

  /**
   * 复制到剪贴板
   */
  copyToClipboard: async (text: string): Promise<boolean> => {
    try {
      await navigator.clipboard.writeText(text)
      return true
    } catch {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      const success = document.execCommand('copy')
      document.body.removeChild(textArea)
      return success
    }
  }
}

// 导出所有工具
export default {
  format,
  validate,
  storage,
  utils
}
