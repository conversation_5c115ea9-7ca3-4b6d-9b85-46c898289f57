package com.example.cllcnplatformbackend.controller;

import com.example.cllcnplatformbackend.utils.Result;
import com.example.cllcnplatformbackend.dto.LoginRequest;
import com.example.cllcnplatformbackend.dto.LoginResponse;
import com.example.cllcnplatformbackend.service.AuthService;
import com.example.cllcnplatformbackend.utils.UserContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 认证控制器
 * 提供用户登录、令牌刷新等认证功能
 * 
 * <AUTHOR> Platform
 * @since 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Validated
@Tag(name = "用户认证", description = "提供用户登录、令牌管理等功能")
public class AuthController {

    private final AuthService authService;

    /**
     * 用户登录
     * 支持邮箱或手机号登录，查询公司管理系统的用户数据
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "支持邮箱或手机号登录，返回JWT令牌和用户信息")
    public Result<LoginResponse> login(@RequestBody @Valid LoginRequest loginRequest) {
        try {
            log.info("用户登录请求 - 账号: {}", loginRequest.getAccount());
            
            LoginResponse response = authService.login(loginRequest);
            
            log.info("用户登录成功 - 平台用户ID: {}", response.getUserInfo().getPlatformUserId());
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("用户登录失败 - 账号: {}, 错误: {}", loginRequest.getAccount(), e.getMessage());
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     * 需要JWT认证
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "根据JWT令牌获取当前登录用户的详细信息")
    public Result<LoginResponse.UserInfo> getCurrentUser() {
        try {
            String currentUserId = UserContext.getCurrentUserId();
            log.info("获取用户信息请求 - 用户ID: {}", currentUserId);
            
            LoginResponse.UserInfo userInfo = authService.getUserInfo(currentUserId);
            
            return Result.success(userInfo);
            
        } catch (Exception e) {
            log.error("获取用户信息失败 - 错误: {}", e.getMessage());
            return Result.error("获取用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 刷新访问令牌
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新访问令牌", description = "使用刷新令牌获取新的访问令牌")
    public Result<Map<String, Object>> refreshToken(
            @RequestBody Map<String, String> request) {
        try {
            String refreshToken = request.get("refreshToken");
            if (refreshToken == null || refreshToken.trim().isEmpty()) {
                return Result.validateFailed("刷新令牌不能为空");
            }
            
            log.info("刷新令牌请求");
            
            String newAccessToken = authService.refreshToken(refreshToken);
            
            Map<String, Object> response = Map.of(
                "accessToken", newAccessToken,
                "tokenType", "Bearer",
                "expiresIn", 7200L
            );
            
            log.info("令牌刷新成功");
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("刷新令牌失败 - 错误: {}", e.getMessage());
            return Result.error("刷新令牌失败：" + e.getMessage());
        }
    }

    /**
     * 用户登出
     * 客户端应该清除本地存储的令牌
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出，客户端应清除本地令牌")
    public Result<Void> logout() {
        try {
            String currentUserId = UserContext.getCurrentUserId();
            log.info("用户登出 - 用户ID: {}", currentUserId);
            
            // 这里可以添加令牌黑名单逻辑，暂时只记录日志
            
            return Result.success();
            
        } catch (Exception e) {
            log.error("用户登出失败 - 错误: {}", e.getMessage());
            return Result.error("登出失败：" + e.getMessage());
        }
    }

    /**
     * 验证令牌有效性
     */
    @PostMapping("/validate")
    @Operation(summary = "验证令牌", description = "验证JWT令牌的有效性")
    public Result<LoginResponse.UserInfo> validateToken(
            @RequestBody Map<String, String> request) {
        try {
            String token = request.get("token");
            if (token == null || token.trim().isEmpty()) {
                return Result.validateFailed("令牌不能为空");
            }
            
            // 移除Bearer前缀
            if (token.startsWith("Bearer ")) {
                token = token.substring(7);
            }
            
            LoginResponse.UserInfo userInfo = authService.validateTokenAndGetUser(token);
            
            return Result.success(userInfo);
            
        } catch (Exception e) {
            log.error("令牌验证失败 - 错误: {}", e.getMessage());
            return Result.error("令牌验证失败：" + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @Operation(summary = "认证服务健康检查", description = "检查认证服务是否正常运行")
    public Result<Map<String, Object>> health() {
        Map<String, Object> health = Map.of(
            "status", "UP",
            "service", "auth-service",
            "timestamp", System.currentTimeMillis()
        );
        return Result.success(health);
    }
}
