/**
 * HTTP客户端工具类
 * 
 * @description 基于Axios封装的HTTP客户端，提供统一的API调用接口
 * <AUTHOR>
 * @date 2025-07-17 10:04:58 +08:00
 * @reference 基于 project_document/architecture/code_architecture_final.md
 */

import axios, { 
  type AxiosInstance, 
  type AxiosRequestConfig, 
  type AxiosResponse, 
  type AxiosError 
} from 'axios'
import { ElMessage, ElLoading } from 'element-plus'
import type { 
  ApiResponse, 
  HttpConfig, 
  RequestOptions, 
  ErrorInfo, 
  ErrorType,
  RetryConfig 
} from '@/types/api'

/**
 * HTTP客户端类
 * 提供统一的HTTP请求接口，包含拦截器、错误处理、重试机制等
 */
export class HttpClient {
  private instance: AxiosInstance
  private loadingInstance: any = null
  private requestCount = 0

  constructor(config: HttpConfig) {
    // 创建axios实例
    this.instance = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout,
      withCredentials: config.withCredentials,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers
      }
    })

    // 设置拦截器
    this.setupRequestInterceptor()
    this.setupResponseInterceptor()
  }

  /**
   * 设置请求拦截器
   */
  private setupRequestInterceptor(): void {
    this.instance.interceptors.request.use(
      (config) => {
        // 添加认证token
        const token = this.getToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // 显示loading
        const options = config as RequestOptions
        if (options.showLoading !== false) {
          this.showLoading()
        }

        return config
      },
      (error) => {
        this.hideLoading()
        return Promise.reject(error)
      }
    )
  }

  /**
   * 设置响应拦截器
   */
  private setupResponseInterceptor(): void {
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        this.hideLoading()

        const { data } = response
        
        // 检查业务状态码
        if (data.code === 200) {
          return data.data
        } else {
          // 业务错误处理
          this.handleBusinessError(data)
          return Promise.reject(new Error(data.message))
        }
      },
      (error: AxiosError) => {
        this.hideLoading()
        return this.handleError(error)
      }
    )
  }

  /**
   * 处理业务错误
   */
  private handleBusinessError(data: ApiResponse): void {
    const errorMap: Record<number, string> = {
      401: '未授权，请重新登录',
      403: '拒绝访问',
      404: '请求的资源不存在',
      500: '服务器内部错误'
    }

    const message = errorMap[data.code] || data.message || '请求失败'
    ElMessage.error(message)
  }

  /**
   * 处理HTTP错误
   */
  private async handleError(error: AxiosError): Promise<never> {
    const errorInfo: ErrorInfo = {
      type: this.getErrorType(error),
      code: error.code || 'UNKNOWN',
      message: error.message,
      timestamp: Date.now(),
      stack: error.stack,
      context: {
        url: error.config?.url,
        method: error.config?.method,
        status: error.response?.status
      }
    }

    // 根据错误类型处理
    switch (errorInfo.type) {
      case 'authentication':
        this.handleAuthError()
        break
      case 'network':
        ElMessage.error('网络连接失败，请检查网络')
        break
      default:
        ElMessage.error(errorInfo.message)
    }

    // 检查是否需要重试
    const config = error.config as RequestOptions
    if (config?.retry && this.shouldRetry(error, config.retry)) {
      return this.retryRequest(config)
    }

    return Promise.reject(errorInfo)
  }

  /**
   * 获取错误类型
   */
  private getErrorType(error: AxiosError): ErrorType {
    if (!error.response) {
      return 'network'
    }

    const status = error.response.status
    if (status === 401) return 'authentication'
    if (status === 403) return 'authorization'
    if (status >= 400 && status < 500) return 'validation'
    if (status >= 500) return 'system'
    
    return 'business'
  }

  /**
   * 处理认证错误
   */
  private handleAuthError(): void {
    this.removeToken()
    ElMessage.error('登录已过期，请重新登录')
    // 跳转到登录页面
    window.location.href = '/login'
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(error: AxiosError, retryConfig: RetryConfig): boolean {
    if (retryConfig.retries <= 0) return false
    
    if (retryConfig.retryCondition) {
      return retryConfig.retryCondition(error)
    }

    // 默认重试条件：网络错误或5xx错误
    return !error.response || (error.response.status >= 500)
  }

  /**
   * 重试请求
   */
  private async retryRequest(config: RequestOptions): Promise<any> {
    const retryConfig = config.retry!
    retryConfig.retries--

    // 延迟重试
    await new Promise(resolve => setTimeout(resolve, retryConfig.retryDelay))

    return this.instance.request(config)
  }

  /**
   * 显示loading
   */
  private showLoading(): void {
    if (this.requestCount === 0) {
      this.loadingInstance = ElLoading.service({
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    }
    this.requestCount++
  }

  /**
   * 隐藏loading
   */
  private hideLoading(): void {
    this.requestCount--
    if (this.requestCount <= 0) {
      this.requestCount = 0
      if (this.loadingInstance) {
        this.loadingInstance.close()
        this.loadingInstance = null
      }
    }
  }

  /**
   * 获取token
   */
  private getToken(): string | null {
    return localStorage.getItem('admin_token')
  }

  /**
   * 移除token
   */
  private removeToken(): void {
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_refresh_token')
  }

  /**
   * 通用请求方法
   */
  public async request<T = any>(config: RequestOptions): Promise<T> {
    return this.instance.request<any, T>(config)
  }

  /**
   * GET请求
   */
  public async get<T = any>(url: string, config?: RequestOptions): Promise<T> {
    return this.request<T>({ ...config, method: 'GET', url })
  }

  /**
   * POST请求
   */
  public async post<T = any>(url: string, data?: any, config?: RequestOptions): Promise<T> {
    return this.request<T>({ ...config, method: 'POST', url, data })
  }

  /**
   * PUT请求
   */
  public async put<T = any>(url: string, data?: any, config?: RequestOptions): Promise<T> {
    return this.request<T>({ ...config, method: 'PUT', url, data })
  }

  /**
   * DELETE请求
   */
  public async delete<T = any>(url: string, config?: RequestOptions): Promise<T> {
    return this.request<T>({ ...config, method: 'DELETE', url })
  }

  /**
   * PATCH请求
   */
  public async patch<T = any>(url: string, data?: any, config?: RequestOptions): Promise<T> {
    return this.request<T>({ ...config, method: 'PATCH', url, data })
  }
}

// 创建默认HTTP客户端实例
const httpClient = new HttpClient({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  withCredentials: true
})

export default httpClient
