package com.example.cllcnplatformbackend.service;

import com.example.cllcnplatformbackend.dto.LoginRequest;
import com.example.cllcnplatformbackend.dto.LoginResponse;

/**
 * 认证服务接口
 * 提供跨系统的用户认证功能
 * 
 * <AUTHOR> Platform
 * @since 2025-07-31
 */
public interface AuthService {

    /**
     * 用户登录
     * 支持邮箱或手机号登录，查询公司管理系统的用户数据
     * 
     * @param loginRequest 登录请求
     * @return 登录响应，包含JWT令牌和用户信息
     */
    LoginResponse login(LoginRequest loginRequest);

    /**
     * 根据平台用户ID获取用户信息
     * 
     * @param platformUserId 平台用户ID（如：E123, C456）
     * @return 用户信息
     */
    LoginResponse.UserInfo getUserInfo(String platformUserId);

    /**
     * 验证JWT令牌并获取用户信息
     * 
     * @param token JWT令牌
     * @return 用户信息
     */
    LoginResponse.UserInfo validateTokenAndGetUser(String token);

    /**
     * 刷新JWT令牌
     * 
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌
     */
    String refreshToken(String refreshToken);
}
