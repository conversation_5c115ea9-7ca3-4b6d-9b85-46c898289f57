/**
 * 路由守卫
 * 
 * @description 路由权限控制和导航守卫
 * <AUTHOR>
 * @date 2025-07-17 10:04:58 +08:00
 * @reference 基于 project_document/architecture/code_architecture_final.md
 * @note 与admin-ui共享相同架构，使用简单的alert提示
 */

import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import type { PermissionCheckResult } from './types'
import { useUserStore } from '@/stores/user'
import { eventBus } from '@/utils/eventBus'

/**
 * 路由守卫类
 */
export class RouteGuards {
  /**
   * 前置守卫 - 权限验证
   */
  static beforeEach(
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ): void {
    // 设置页面标题
    if (to.meta?.title) {
      document.title = `${to.meta.title} - CLLCN国际物流`
    }

    // 检查是否需要认证
    if (to.meta?.requiresAuth !== false) {
      const userStore = useUserStore()

      if (!userStore.isAuthenticated) {
        // 使用事件总线触发登录弹窗
        eventBus.emit('ui:show-login-modal', { redirectPath: to.fullPath })
        next(false) // 阻止导航
        return
      }

      // 检查权限
      const permissionResult = RouteGuards.checkPermissions(to)
      if (!permissionResult.hasPermission) {
        alert('您没有访问该页面的权限')
        
        if (permissionResult.redirectTo) {
          next(permissionResult.redirectTo)
        } else {
          next('/403')
        }
        return
      }
    }

    // 处理外部链接
    if (to.meta?.external) {
      window.open(to.path as string, (to.meta.target as string) || '_blank')
      next(false)
      return
    }

    next()
  }

  /**
   * 后置守卫 - 页面加载完成处理
   */
  static afterEach(
    to: RouteLocationNormalized,
    _from: RouteLocationNormalized
  ): void {
    // 更新面包屑
    RouteGuards.updateBreadcrumbs(to)
    
    // 更新底部导航状态
    RouteGuards.updateTabbar(to)
    
    // 页面加载完成，隐藏loading
    const loadingElement = document.getElementById('app-loading')
    if (loadingElement) {
      loadingElement.style.display = 'none'
    }
  }

  /**
   * 检查路由权限
   */
  static checkPermissions(route: RouteLocationNormalized): PermissionCheckResult {
    const userStore = useUserStore()
    const { permissions: userPermissions, roles: userRoles } = userStore
    
    const requiredPermissions = (route.meta?.permissions || []) as string[]
    const requiredRoles = (route.meta?.roles || []) as string[]

    // 检查权限
    const missingPermissions = requiredPermissions.filter(
      (permission: string) => !userPermissions.includes(permission)
    )

    // 检查角色
    const missingRoles = requiredRoles.filter(
      (role: string) => !userRoles.includes(role)
    )

    const hasPermission = missingPermissions.length === 0 && missingRoles.length === 0

    return {
      hasPermission,
      missingPermissions: missingPermissions.length > 0 ? missingPermissions : undefined,
      missingRoles: missingRoles.length > 0 ? missingRoles : undefined,
      redirectTo: !hasPermission ? '/403' : undefined
    }
  }

  /**
   * 更新面包屑
   */
  private static updateBreadcrumbs(route: RouteLocationNormalized): void {
    // 这里可以实现面包屑更新逻辑
    // 可以通过store或者事件总线来通知面包屑组件更新
    console.log('更新面包屑:', route.path)
  }

  /**
   * 更新底部导航状态
   */
  private static updateTabbar(route: RouteLocationNormalized): void {
    // 这里可以实现底部导航状态更新逻辑
    if (route.meta?.showInTabbar) {
      console.log('更新底部导航:', route.path)
    }
  }

  /**
   * 检查用户是否有指定权限
   */
  static hasPermission(permission: string): boolean {
    const userStore = useUserStore()
    return userStore.permissions.includes(permission)
  }

  /**
   * 检查用户是否有指定角色
   */
  static hasRole(role: string): boolean {
    const userStore = useUserStore()
    return userStore.roles.includes(role)
  }

  /**
   * 检查用户是否有任一权限
   */
  static hasAnyPermission(permissions: string[]): boolean {
    const userStore = useUserStore()
    return permissions.some(permission => userStore.permissions.includes(permission))
  }

  /**
   * 检查用户是否有任一角色
   */
  static hasAnyRole(roles: string[]): boolean {
    const userStore = useUserStore()
    return roles.some(role => userStore.roles.includes(role))
  }
}
