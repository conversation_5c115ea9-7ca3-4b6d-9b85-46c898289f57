package com.example.cllcnplatformbackend.service.impl;

import com.example.cllcnplatformbackend.dto.LoginRequest;
import com.example.cllcnplatformbackend.dto.LoginResponse;
import com.example.cllcnplatformbackend.entity.company.Client;
import com.example.cllcnplatformbackend.entity.company.Employee;
import com.example.cllcnplatformbackend.repository.company.ClientRepository;
import com.example.cllcnplatformbackend.repository.company.EmployeeRepository;
import com.example.cllcnplatformbackend.service.AuthService;
import com.example.cllcnplatformbackend.utils.JwtUtil;
import com.example.cllcnplatformbackend.utils.Md5Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import io.jsonwebtoken.Claims;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证服务实现类
 * 实现跨系统的用户认证功能
 * 
 * <AUTHOR> Platform
 * @since 2025-07-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final EmployeeRepository employeeRepository;
    private final ClientRepository clientRepository;
    private final JwtUtil jwtUtil;

    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        String account = loginRequest.getAccount().trim();
        String password = loginRequest.getPassword().trim();
        
        log.info("用户登录尝试 - 账号: {}", account);

        // 首先尝试员工登录（邮箱）
        Employee employee = employeeRepository.findByEmailAndStatus(account, "Active");
        if (employee != null && employee.getPassword() != null) {
            if (Md5Util.getMD5(password).equals(employee.getPassword())) {
                log.info("员工登录成功 - ID: {}, 姓名: {}", employee.getEmployeeId(), employee.getName());
                return createLoginResponse(employee);
            }
        }

        // 尝试员工登录（手机号）
        if (employee == null) {
            employee = employeeRepository.findByPhoneAndStatus(account, "Active");
            if (employee != null && employee.getPassword() != null) {
                if (Md5Util.getMD5(password).equals(employee.getPassword())) {
                    log.info("员工登录成功 - ID: {}, 姓名: {}", employee.getEmployeeId(), employee.getName());
                    return createLoginResponse(employee);
                }
            }
        }

        // 尝试客户登录（邮箱）
        Client client = clientRepository.findByEmailAndStatusIn(account, java.util.Arrays.asList("审核通过", "已合作"));
        if (client != null && client.getPassword() != null) {
            if (Md5Util.getMD5(password).equals(client.getPassword())) {
                log.info("客户登录成功 - ID: {}, 姓名: {}", client.getClientId(), client.getName());
                return createLoginResponse(client);
            }
        }

        // 尝试客户登录（手机号）
        if (client == null) {
            client = clientRepository.findByPhoneAndStatusIn(account, java.util.Arrays.asList("审核通过", "已合作"));
            if (client != null && client.getPassword() != null) {
                if (Md5Util.getMD5(password).equals(client.getPassword())) {
                    log.info("客户登录成功 - ID: {}, 姓名: {}", client.getClientId(), client.getName());
                    return createLoginResponse(client);
                }
            }
        }

        log.warn("登录失败 - 账号: {}", account);
        throw new RuntimeException("账号或密码错误");
    }

    @Override
    public LoginResponse.UserInfo getUserInfo(String platformUserId) {
        if (platformUserId.startsWith("E")) {
            // 员工用户
            Integer employeeId = Integer.parseInt(platformUserId.substring(1));
            Employee employee = employeeRepository.findByEmployeeIdAndStatus(employeeId, "Active");
            if (employee != null) {
                return createUserInfo(employee);
            }
        } else if (platformUserId.startsWith("C")) {
            // 客户用户
            Integer clientId = Integer.parseInt(platformUserId.substring(1));
            Client client = clientRepository.findByClientIdAndStatusIn(clientId, java.util.Arrays.asList("审核通过", "已合作"));
            if (client != null) {
                return createUserInfo(client);
            }
        }
        
        throw new RuntimeException("用户不存在或已被禁用");
    }

    @Override
    public LoginResponse.UserInfo validateTokenAndGetUser(String token) {
        try {
            Claims claims = jwtUtil.parseToken(token);
            String platformUserId = (String) claims.get("platformUserId");
            return getUserInfo(platformUserId);
        } catch (Exception e) {
            log.error("Token验证失败", e);
            throw new RuntimeException("Token无效或已过期");
        }
    }

    @Override
    public String refreshToken(String refreshToken) {
        try {
            Claims claims = jwtUtil.parseToken(refreshToken);
            String platformUserId = (String) claims.get("platformUserId");
            
            // 验证用户仍然有效
            getUserInfo(platformUserId);
            
            // 生成新的访问令牌
            Map<String, Object> newClaims = new HashMap<>();
            newClaims.put("platformUserId", platformUserId);
            newClaims.put("userType", claims.get("userType"));
            newClaims.put("realName", claims.get("realName"));
            
            return jwtUtil.generateToken(newClaims);
        } catch (Exception e) {
            log.error("刷新Token失败", e);
            throw new RuntimeException("刷新Token失败");
        }
    }

    /**
     * 为员工创建登录响应
     */
    private LoginResponse createLoginResponse(Employee employee) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("platformUserId", "E" + employee.getEmployeeId());
        claims.put("userType", mapEmployeeRole(employee.getRole().toString()));
        claims.put("realName", employee.getName());
        claims.put("email", employee.getEmail());
        claims.put("mobile", employee.getPhone());

        String token = jwtUtil.generateToken(claims);
        Long expiresIn = JwtUtil.getExpirationTime();

        LoginResponse.UserInfo userInfo = createUserInfo(employee);
        
        return LoginResponse.success(token, expiresIn, userInfo);
    }

    /**
     * 为客户创建登录响应
     */
    private LoginResponse createLoginResponse(Client client) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("platformUserId", "C" + client.getClientId());
        claims.put("userType", "client");
        claims.put("realName", client.getName());
        claims.put("email", client.getEmail());
        claims.put("mobile", client.getPhone());

        String token = jwtUtil.generateToken(claims);
        Long expiresIn = JwtUtil.getExpirationTime();

        LoginResponse.UserInfo userInfo = createUserInfo(client);
        
        return LoginResponse.success(token, expiresIn, userInfo);
    }

    /**
     * 创建员工用户信息
     */
    private LoginResponse.UserInfo createUserInfo(Employee employee) {
        return new LoginResponse.UserInfo(
            "E" + employee.getEmployeeId(),
            mapEmployeeRole(employee.getRole().toString()),
            employee.getName(),
            employee.getEmail(),
            employee.getPhone(),
            "active"
        );
    }

    /**
     * 创建客户用户信息
     */
    private LoginResponse.UserInfo createUserInfo(Client client) {
        return new LoginResponse.UserInfo(
            "C" + client.getClientId(),
            "client",
            client.getName(),
            client.getEmail(),
            client.getPhone(),
            mapClientStatus(client.getStatus())
        );
    }

    /**
     * 映射员工角色
     */
    private String mapEmployeeRole(String role) {
        if (role == null) return "employee";
        switch (role.toUpperCase()) {
            case "ADMIN":
                return "admin";
            case "MANAGER":
                return "manager";
            default:
                return "employee";
        }
    }

    /**
     * 映射客户状态
     */
    private String mapClientStatus(String status) {
        if ("审核通过".equals(status) || "已合作".equals(status)) {
            return "active";
        }
        return "inactive";
    }
}
