package com.example.cllcnplatformbackend.entity.company;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 客户实体类（来自公司管理系统）
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "client")
public class Client {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "client_id")
    private Long clientId;
    
    /**
     * 客户姓名
     */
    @Column(name = "name", nullable = false, length = 50)
    private String name;
    
    /**
     * 邮箱
     */
    @Column(name = "email", nullable = false, length = 100, unique = true)
    private String email;
    
    /**
     * 密码（加密）- 新增字段
     */
    @Column(name = "password", length = 255)
    private String password;
    
    /**
     * 手机号
     */
    @Column(name = "phone", length = 20)
    private String phone;
    
    /**
     * 公司名称
     */
    @Column(name = "company_name", length = 100)
    private String companyName;
    
    /**
     * 联系人
     */
    @Column(name = "contact_person", length = 50)
    private String contactPerson;
    
    /**
     * 地址
     */
    @Column(name = "address", length = 200)
    private String address;
    
    /**
     * 状态：审核通过, 待审核, 审核拒绝, 已合作
     */
    @Column(name = "status", nullable = false, length = 20)
    private String status;
    
    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;
    
    /**
     * 判断是否为激活状态（审核通过或已合作）
     */
    public boolean isActive() {
        return "审核通过".equals(this.status) || "已合作".equals(this.status);
    }
    
    /**
     * 判断是否有密码（可以登录）
     */
    public boolean hasPassword() {
        return password != null && !password.trim().isEmpty();
    }
    
    /**
     * 获取标准化状态
     * 用于与Employee状态统一
     */
    public String getStandardStatus() {
        return isActive() ? "ACTIVE" : "INACTIVE";
    }
}
