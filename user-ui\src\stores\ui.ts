/**
 * UI状态管理
 * 
 * @description 全局UI状态管理，包括弹窗、加载状态等
 * <AUTHOR>
 * @date 2025-07-18 10:30:00 +08:00
 * @reference 基于 project_document/architecture/code_architecture_final.md
 * @note 管理登录弹窗、注册弹窗等全局UI状态
 */

import { defineStore } from 'pinia'
import { ref, onMounted, onUnmounted } from 'vue'
import { eventBus } from '@/utils/eventBus'

export const useUIStore = defineStore('ui', () => {
  // 登录弹窗状态
  const showLoginModal = ref(false)
  
  // 注册弹窗状态
  const showRegisterModal = ref(false)
  
  // 全局加载状态
  const globalLoading = ref(false)
  
  // 登录重定向路径
  const loginRedirectPath = ref<string>('/')

  /**
   * 打开登录弹窗
   */
  const openLoginModal = (redirectPath?: string): void => {
    showLoginModal.value = true
    if (redirectPath) {
      loginRedirectPath.value = redirectPath
    }
  }

  /**
   * 关闭登录弹窗
   */
  const closeLoginModal = (): void => {
    showLoginModal.value = false
  }

  /**
   * 打开注册弹窗
   */
  const openRegisterModal = (): void => {
    showRegisterModal.value = true
  }

  /**
   * 关闭注册弹窗
   */
  const closeRegisterModal = (): void => {
    showRegisterModal.value = false
  }

  /**
   * 切换到注册弹窗（从登录弹窗）
   */
  const switchToRegister = (): void => {
    closeLoginModal()
    openRegisterModal()
  }

  /**
   * 切换到登录弹窗（从注册弹窗）
   */
  const switchToLogin = (): void => {
    closeRegisterModal()
    openLoginModal()
  }

  /**
   * 设置全局加载状态
   */
  const setGlobalLoading = (loading: boolean): void => {
    globalLoading.value = loading
  }

  /**
   * 关闭所有弹窗
   */
  const closeAllModals = (): void => {
    showLoginModal.value = false
    showRegisterModal.value = false
  }

  /**
   * 初始化事件监听器
   */
  const initEventListeners = (): void => {
    // 监听显示登录弹窗事件
    eventBus.on('ui:show-login-modal', ({ redirectPath }) => {
      openLoginModal(redirectPath)
    })

    // 监听显示注册弹窗事件
    eventBus.on('ui:show-register-modal', () => {
      openRegisterModal()
    })

    // 监听关闭所有弹窗事件
    eventBus.on('ui:close-all-modals', () => {
      closeAllModals()
    })
  }

  /**
   * 清理事件监听器
   */
  const cleanupEventListeners = (): void => {
    eventBus.off('ui:show-login-modal')
    eventBus.off('ui:show-register-modal')
    eventBus.off('ui:close-all-modals')
  }

  // 初始化事件监听器
  initEventListeners()

  return {
    // 状态
    showLoginModal,
    showRegisterModal,
    globalLoading,
    loginRedirectPath,
    
    // 方法
    openLoginModal,
    closeLoginModal,
    openRegisterModal,
    closeRegisterModal,
    switchToRegister,
    switchToLogin,
    setGlobalLoading,
    closeAllModals
  }
})
