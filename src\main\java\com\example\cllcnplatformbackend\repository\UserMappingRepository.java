package com.example.cllcnplatformbackend.repository.logistics;

import com.example.cllcnplatformbackend.entity.logistics.UserMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 用户映射Repository
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Repository
public interface UserMappingRepository extends JpaRepository<UserMapping, Long> {
    
    /**
     * 根据原始用户ID和用户类型查找用户
     */
    Optional<UserMapping> findByOriginalUserIdAndUserType(Long originalUserId, UserMapping.UserType userType);
    
    /**
     * 根据用户名查找用户
     */
    Optional<UserMapping> findByUsername(String username);
    
    /**
     * 根据邮箱查找用户
     */
    Optional<UserMapping> findByEmail(String email);
    
    /**
     * 根据手机号查找用户
     */
    Optional<UserMapping> findByPhone(String phone);
    
    /**
     * 根据用户名和密码查找用户（用于登录验证）
     * 注意：这里只是查找用户，密码验证需要在Service层进行
     */
    @Query("SELECT u FROM UserMapping u WHERE u.username = :username AND u.status = 'ACTIVE'")
    Optional<UserMapping> findActiveUserByUsername(@Param("username") String username);
    
    /**
     * 根据邮箱和密码查找用户（用于登录验证）
     */
    @Query("SELECT u FROM UserMapping u WHERE u.email = :email AND u.status = 'ACTIVE'")
    Optional<UserMapping> findActiveUserByEmail(@Param("email") String email);
    
    /**
     * 根据手机号和密码查找用户（用于登录验证）
     */
    @Query("SELECT u FROM UserMapping u WHERE u.phone = :phone AND u.status = 'ACTIVE'")
    Optional<UserMapping> findActiveUserByPhone(@Param("phone") String phone);
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);
    
    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);
    
    /**
     * 根据用户类型查找所有用户
     */
    @Query("SELECT u FROM UserMapping u WHERE u.userType = :userType AND u.status != 'DELETED' ORDER BY u.createdAt DESC")
    java.util.List<UserMapping> findByUserTypeAndNotDeleted(@Param("userType") UserMapping.UserType userType);
    
    /**
     * 根据部门ID查找员工
     */
    @Query("SELECT u FROM UserMapping u WHERE u.userType = 'EMPLOYEE' AND u.departmentId = :departmentId AND u.status = 'ACTIVE'")
    java.util.List<UserMapping> findActiveEmployeesByDepartmentId(@Param("departmentId") Long departmentId);
    
    /**
     * 根据公司ID查找客户
     */
    @Query("SELECT u FROM UserMapping u WHERE u.userType = 'CLIENT' AND u.companyId = :companyId AND u.status = 'ACTIVE'")
    java.util.List<UserMapping> findActiveClientsByCompanyId(@Param("companyId") Long companyId);
}
