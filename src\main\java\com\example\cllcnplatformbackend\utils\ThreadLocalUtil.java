package com.example.cllcnplatformbackend.utils;

/**
 * ThreadLocal工具类
 * 用于在当前线程中存储和获取用户信息
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
public class ThreadLocalUtil {
    
    /**
     * 存储用户信息的ThreadLocal
     */
    private static final ThreadLocal<Object> THREAD_LOCAL = new ThreadLocal<>();
    
    /**
     * 设置当前线程的用户信息
     *
     * @param value 用户信息
     */
    public static void set(Object value) {
        THREAD_LOCAL.set(value);
    }
    
    /**
     * 获取当前线程的用户信息
     *
     * @return 用户信息
     */
    public static Object get() {
        return THREAD_LOCAL.get();
    }
    
    /**
     * 获取当前线程的用户信息（指定类型）
     *
     * @param clazz 类型
     * @param <T> 泛型类型
     * @return 用户信息
     */
    @SuppressWarnings("unchecked")
    public static <T> T get(Class<T> clazz) {
        Object value = THREAD_LOCAL.get();
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 移除当前线程的用户信息
     */
    public static void remove() {
        THREAD_LOCAL.remove();
    }
    
    /**
     * 判断当前线程是否有用户信息
     *
     * @return 是否有用户信息
     */
    public static boolean hasValue() {
        return THREAD_LOCAL.get() != null;
    }
}
