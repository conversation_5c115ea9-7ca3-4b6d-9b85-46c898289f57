package com.example.cllcnplatformbackend.repository.company;

import com.example.cllcnplatformbackend.entity.company.Employee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 员工Repository（公司管理系统）
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Repository
public interface EmployeeRepository extends JpaRepository<Employee, Long> {
    
    /**
     * 根据邮箱查找员工
     */
    Optional<Employee> findByEmail(String email);
    
    /**
     * 根据手机号查找员工
     */
    Optional<Employee> findByPhone(String phone);
    
    /**
     * 根据邮箱查找激活状态的员工（用于登录）
     */
    @Query("SELECT e FROM Employee e WHERE e.email = :email AND e.status = 'ACTIVE'")
    Optional<Employee> findActiveByEmail(@Param("email") String email);
    
    /**
     * 根据手机号查找激活状态的员工（用于登录）
     */
    @Query("SELECT e FROM Employee e WHERE e.phone = :phone AND e.status = 'ACTIVE'")
    Optional<Employee> findActiveByPhone(@Param("phone") String phone);
    
    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);
    
    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);
    
    /**
     * 根据部门ID查找激活员工
     */
    @Query("SELECT e FROM Employee e WHERE e.departmentId = :departmentId AND e.status = 'ACTIVE'")
    java.util.List<Employee> findActiveByDepartmentId(@Param("departmentId") Long departmentId);
    
    /**
     * 根据角色查找激活员工
     */
    @Query("SELECT e FROM Employee e WHERE e.role = :role AND e.status = 'ACTIVE'")
    java.util.List<Employee> findActiveByRole(@Param("role") Employee.Role role);

    /**
     * 根据邮箱和状态查找员工（用于认证）
     */
    Employee findByEmailAndStatus(String email, String status);

    /**
     * 根据手机号和状态查找员工（用于认证）
     */
    Employee findByPhoneAndStatus(String phone, String status);

    /**
     * 根据员工ID和状态查找员工（用于认证）
     */
    Employee findByEmployeeIdAndStatus(Integer employeeId, String status);
}
