package com.example.cllcnplatformbackend.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OpenAPI配置
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Configuration
public class OpenApiConfig {
    
    @Value("${app.upload.base-url}")
    private String baseUrl;
    
    /**
     * OpenAPI配置
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(List.of(
                        new Server().url(baseUrl).description("开发环境"),
                        new Server().url("https://api.cllcn.com").description("生产环境")
                ))
                .components(new Components()
                        .addSecuritySchemes("Bearer", new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                                .description("JWT认证")
                        )
                )
                .addSecurityItem(new SecurityRequirement().addList("Bearer"));
    }
    
    /**
     * API信息
     */
    private Info apiInfo() {
        return new Info()
                .title("CLLCN国际物流平台API")
                .description("CLLCN国际物流平台后端API接口文档")
                .version("1.0.0")
                .contact(new Contact()
                        .name("CLLCN开发团队")
                        .email("<EMAIL>")
                        .url("https://www.cllcn.com")
                )
                .license(new License()
                        .name("MIT License")
                        .url("https://opensource.org/licenses/MIT")
                );
    }
}
