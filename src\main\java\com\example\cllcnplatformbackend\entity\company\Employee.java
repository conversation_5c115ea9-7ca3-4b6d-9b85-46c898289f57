package com.example.cllcnplatformbackend.entity.company;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 员工实体类（来自公司管理系统）
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "employee")
public class Employee {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "employee_id")
    private Long employeeId;
    
    /**
     * 姓名
     */
    @Column(name = "name", nullable = false, length = 50)
    private String name;
    
    /**
     * 邮箱
     */
    @Column(name = "email", nullable = false, length = 100, unique = true)
    private String email;
    
    /**
     * 手机号
     */
    @Column(name = "phone", nullable = false, length = 20, unique = true)
    private String phone;
    
    /**
     * 身份证号
     */
    @Column(name = "id_card", length = 18)
    private String idCard;
    
    /**
     * 密码（加密）
     */
    @Column(name = "password", nullable = false, length = 255)
    private String password;
    
    /**
     * 角色：ADMIN, USER
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "role", nullable = false, length = 20)
    private Role role;
    
    /**
     * 状态：Active, Inactive
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private Status status;
    
    /**
     * 部门ID
     */
    @Column(name = "department_id")
    private Long departmentId;
    
    /**
     * 职位ID
     */
    @Column(name = "position_id")
    private Long positionId;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;
    
    /**
     * 角色枚举
     */
    public enum Role {
        ADMIN,  // 管理员
        USER    // 普通用户
    }
    
    /**
     * 状态枚举
     */
    public enum Status {
        ACTIVE,   // 激活
        INACTIVE  // 未激活
    }
    
    /**
     * 判断是否为管理员
     */
    public boolean isAdmin() {
        return Role.ADMIN.equals(this.role);
    }
    
    /**
     * 判断是否为激活状态
     */
    public boolean isActive() {
        return Status.ACTIVE.equals(this.status);
    }
}
