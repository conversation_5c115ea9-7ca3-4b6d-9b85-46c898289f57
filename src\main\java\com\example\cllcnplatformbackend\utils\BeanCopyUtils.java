package com.example.cllcnplatformbackend.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

/**
 * Bean拷贝工具类
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Slf4j
public class BeanCopyUtils {
    
    /**
     * 单个对象拷贝
     *
     * @param source 源对象
     * @param targetClass 目标类
     * @param <T> 目标类型
     * @return 目标对象
     */
    public static <T> T copyBean(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            log.error("Bean拷贝失败", e);
            throw new RuntimeException("Bean拷贝失败", e);
        }
    }
    
    /**
     * 单个对象拷贝（使用Supplier）
     *
     * @param source 源对象
     * @param targetSupplier 目标对象供应商
     * @param <T> 目标类型
     * @return 目标对象
     */
    public static <T> T copyBean(Object source, Supplier<T> targetSupplier) {
        if (source == null) {
            return null;
        }
        
        try {
            T target = targetSupplier.get();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            log.error("Bean拷贝失败", e);
            throw new RuntimeException("Bean拷贝失败", e);
        }
    }
    
    /**
     * 列表对象拷贝
     *
     * @param sourceList 源列表
     * @param targetClass 目标类
     * @param <T> 目标类型
     * @return 目标列表
     */
    public static <T> List<T> copyBeanList(List<?> sourceList, Class<T> targetClass) {
        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (Object source : sourceList) {
            T target = copyBean(source, targetClass);
            if (target != null) {
                targetList.add(target);
            }
        }
        
        return targetList;
    }
    
    /**
     * 列表对象拷贝（使用Supplier）
     *
     * @param sourceList 源列表
     * @param targetSupplier 目标对象供应商
     * @param <T> 目标类型
     * @return 目标列表
     */
    public static <T> List<T> copyBeanList(List<?> sourceList, Supplier<T> targetSupplier) {
        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (Object source : sourceList) {
            T target = copyBean(source, targetSupplier);
            if (target != null) {
                targetList.add(target);
            }
        }
        
        return targetList;
    }
    
    /**
     * 拷贝属性（忽略null值）
     *
     * @param source 源对象
     * @param target 目标对象
     */
    public static void copyPropertiesIgnoreNull(Object source, Object target) {
        if (source == null || target == null) {
            return;
        }
        
        try {
            BeanUtils.copyProperties(source, target);
        } catch (Exception e) {
            log.error("Bean属性拷贝失败", e);
            throw new RuntimeException("Bean属性拷贝失败", e);
        }
    }
}
