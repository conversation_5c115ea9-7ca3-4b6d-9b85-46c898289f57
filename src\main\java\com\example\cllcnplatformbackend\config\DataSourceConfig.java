package com.example.cllcnplatformbackend.config;

import com.zaxxer.hikari.HikariDataSource;
import jakarta.persistence.EntityManagerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * 混合架构数据源配置
 * 用户认证共享，业务数据独立
 *
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
@Configuration
@EnableTransactionManagement
public class DataSourceConfig {

    /**
     * 公司管理系统数据源（用户认证共享）
     */
    @Primary
    @Bean(name = "companyDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.company")
    public DataSource companyDataSource() {
        return DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
    }

    /**
     * 物流平台数据源（业务数据独立）
     */
    @Bean(name = "logisticsDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.logistics")
    public DataSource logisticsDataSource() {
        return DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
    }

    /**
     * 公司管理系统EntityManagerFactory
     */
    @Primary
    @Bean(name = "companyEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean companyEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("companyDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.example.cllcnplatformbackend.entity.company")
                .persistenceUnit("company")
                .build();
    }

    /**
     * 物流平台EntityManagerFactory
     */
    @Bean(name = "logisticsEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean logisticsEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("logisticsDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.example.cllcnplatformbackend.entity.logistics")
                .persistenceUnit("logistics")
                .build();
    }

    /**
     * 公司管理系统事务管理器
     */
    @Primary
    @Bean(name = "companyTransactionManager")
    public PlatformTransactionManager companyTransactionManager(
            @Qualifier("companyEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }

    /**
     * 物流平台事务管理器
     */
    @Bean(name = "logisticsTransactionManager")
    public PlatformTransactionManager logisticsTransactionManager(
            @Qualifier("logisticsEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }

    /**
     * 公司管理系统Repository配置
     */
    @Configuration
    @EnableJpaRepositories(
            basePackages = "com.example.cllcnplatformbackend.repository.company",
            entityManagerFactoryRef = "companyEntityManagerFactory",
            transactionManagerRef = "companyTransactionManager"
    )
    static class CompanyRepositoryConfig {
    }

    /**
     * 物流平台Repository配置
     */
    @Configuration
    @EnableJpaRepositories(
            basePackages = "com.example.cllcnplatformbackend.repository.logistics",
            entityManagerFactoryRef = "logisticsEntityManagerFactory",
            transactionManagerRef = "logisticsTransactionManager"
    )
    static class LogisticsRepositoryConfig {
    }
}
