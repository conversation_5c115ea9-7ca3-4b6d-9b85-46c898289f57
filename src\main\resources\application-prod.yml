# 生产环境配置
server:
  port: ${SERVER_PORT:8081}

spring:
  # 数据源配置 - 混合架构（用户认证共享，业务数据独立）
  datasource:
    # 公司管理系统数据库（用户认证共享）
    company:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: jdbc:mysql://${DB_COMPANY_HOST:localhost}:${DB_COMPANY_PORT:3306}/company_management_system?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf-8&useSSL=true&allowPublicKeyRetrieval=true
      username: ${DB_COMPANY_USERNAME}
      password: ${DB_COMPANY_PASSWORD}
      hikari:
        maximum-pool-size: 50
        minimum-idle: 10
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        leak-detection-threshold: 60000

    # 物流平台数据库（业务数据独立）
    logistics:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: jdbc:mysql://${DB_LOGISTICS_HOST:localhost}:${DB_LOGISTICS_PORT:3306}/logistics_platform?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf-8&useSSL=true&allowPublicKeyRetrieval=true
      username: ${DB_LOGISTICS_USERNAME}
      password: ${DB_LOGISTICS_PASSWORD}
      hikari:
        maximum-pool-size: 50
        minimum-idle: 10
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        leak-detection-threshold: 60000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: none  # 生产环境不允许自动修改表结构
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false
        use_sql_comments: false
        jdbc:
          batch_size: 50
        order_inserts: true
        order_updates: true
  
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD}
      database: ${REDIS_DATABASE:0}
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 50
          max-idle: 20
          min-idle: 10
          max-wait: 5000ms
  
  # SQL初始化
  sql:
    init:
      mode: never  # 生产环境不自动执行SQL脚本

# 文件上传配置
app:
  upload:
    path: ${UPLOAD_PATH:/data/uploads}  # 生产环境使用环境变量
    base-url: ${APP_BASE_URL}  # 生产环境地址
  
  # 跨域配置
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS}
    allowed-methods: ${CORS_ALLOWED_METHODS:GET,POST,PUT,DELETE,OPTIONS}
    allowed-headers: ${CORS_ALLOWED_HEADERS:*}
    allow-credentials: true
    max-age: 3600

# JWT配置
jwt:
  secret: ${JWT_SECRET}  # 生产环境必须使用环境变量
  expiration: ${JWT_EXPIRATION:86400000}  # 24小时
  header: Authorization
  token-prefix: Bearer

# 日志配置
logging:
  level:
    com.example.cllcnplatformbackend: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  file:
    name: ${LOG_FILE:/data/logs/cllcn-logistics.log}
  logback:
    rollingpolicy:
      max-file-size: 500MB
      max-history: 90
      total-size-cap: 10GB

# SpringDoc配置
springdoc:
  api-docs:
    enabled: ${API_DOCS_ENABLED:false}  # 生产环境默认关闭API文档
    path: /v3/api-docs
  swagger-ui:
    enabled: ${SWAGGER_UI_ENABLED:false}  # 生产环境默认关闭Swagger UI
    path: /swagger-ui.html
  show-actuator: false

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics  # 生产环境只暴露必要端点
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  security:
    enabled: true
