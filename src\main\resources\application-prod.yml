# 生产环境配置
server:
  port: 8081
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

spring:
  # 应用信息
  application:
    name: cllcn-logistics-platform

  # Web配置
  web:
    resources:
      add-mappings: false  # 禁用默认静态资源处理，避免与API路径冲突

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

  # 数据源配置 - 混合架构（用户认证共享，业务数据独立）
  datasource:
    # 公司管理系统数据库（用户认证共享）
    company:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ************************************************************************************************************************************************************************
      username: cllcn_prod_user
      password: cllcn_prod_password_2025
      hikari:
        maximum-pool-size: 50
        minimum-idle: 10
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        leak-detection-threshold: 60000

    # 物流平台数据库（业务数据独立）
    logistics:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *****************************************************************************************************************************************************************
      username: cllcn_prod_user
      password: cllcn_prod_password_2025
      hikari:
        maximum-pool-size: 50
        minimum-idle: 10
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        leak-detection-threshold: 60000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: none  # 生产环境不允许自动修改表结构
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: false
        use_sql_comments: false
        generate_statistics: false
        jdbc:
          batch_size: 50
        order_inserts: true
        order_updates: true

  # Redis配置
  data:
    redis:
      host: prod-redis-server
      port: 6379
      password: redis_prod_password_2025
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 50
          max-idle: 20
          min-idle: 10
          max-wait: 5000ms

  # SQL初始化
  sql:
    init:
      mode: never  # 生产环境不自动执行SQL脚本

# JWT配置
jwt:
  secret: cllcn_logistics_platform_prod_secret_key_2025_secure
  expiration: 86400000  # 24小时
  header: Authorization
  token-prefix: Bearer

# 自定义应用配置
app:
  upload:
    # 物理存储根路径
    path: /data/uploads
    # URL访问前缀
    url-prefix: /uploads
    # 应用基础URL
    base-url: https://api.cllcn.com

  # 跨域配置
  cors:
    allowed-origins: https://admin.cllcn.com,https://www.cllcn.com
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600

# 日志配置
logging:
  level:
    com.example.cllcnplatformbackend: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /data/logs/cllcn-logistics.log
  logback:
    rollingpolicy:
      max-file-size: 500MB
      max-history: 90
      total-size-cap: 10GB

# SpringDoc配置
springdoc:
  api-docs:
    enabled: false  # 生产环境关闭API文档
    path: /v3/api-docs
  swagger-ui:
    enabled: false  # 生产环境关闭Swagger UI
    path: /swagger-ui.html
  show-actuator: false

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics  # 生产环境只暴露必要端点
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  security:
    enabled: true

# 性能优化配置
spring.jpa:
  show-sql: false
  hibernate:
    ddl-auto: none
  properties:
    hibernate:
      format_sql: false
      use_sql_comments: false
      generate_statistics: false

# 缓存配置
spring.cache:
  type: simple
  cache-names:
    - userCache
    - departmentCache
    - promotionCache
