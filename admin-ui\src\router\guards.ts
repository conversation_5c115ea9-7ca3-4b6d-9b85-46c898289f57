/**
 * 路由守卫
 * 
 * @description 路由权限控制和导航守卫
 * <AUTHOR>
 * @date 2025-07-17 10:04:58 +08:00
 * @reference 基于 project_document/architecture/code_architecture_final.md
 */

import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import type { PermissionCheckResult } from './types'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

/**
 * 路由守卫类
 */
export class RouteGuards {
  /**
   * 前置守卫 - 权限验证
   */
  static beforeEach(
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ): void {
    // 设置页面标题
    if (to.meta?.title) {
      document.title = `${to.meta.title} - CLLCN管理后台`
    }

    // 检查是否需要认证
    if (to.meta?.requiresAuth !== false) {
      const userStore = useUserStore()
      
      if (!userStore.isAuthenticated) {
        ElMessage.warning('请先登录')
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }

      // 检查权限
      const permissionResult = RouteGuards.checkPermissions(to)
      if (!permissionResult.hasPermission) {
        ElMessage.error('您没有访问该页面的权限')
        
        if (permissionResult.redirectTo) {
          next(permissionResult.redirectTo)
        } else {
          next('/403')
        }
        return
      }
    }

    // 处理外部链接
    if (to.meta?.external) {
      window.open(to.path, to.meta.target || '_blank')
      next(false)
      return
    }

    next()
  }

  /**
   * 后置守卫 - 页面加载完成处理
   */
  static afterEach(
    to: RouteLocationNormalized,
    from: RouteLocationNormalized
  ): void {
    // 更新面包屑
    RouteGuards.updateBreadcrumbs(to)
    
    // 添加到标签页
    RouteGuards.addToTabs(to)
    
    // 页面加载完成，隐藏loading
    const loadingElement = document.getElementById('app-loading')
    if (loadingElement) {
      loadingElement.style.display = 'none'
    }
  }

  /**
   * 检查路由权限
   */
  static checkPermissions(route: RouteLocationNormalized): PermissionCheckResult {
    const userStore = useUserStore()
    const { permissions: userPermissions, roles: userRoles } = userStore
    
    const requiredPermissions = route.meta?.permissions || []
    const requiredRoles = route.meta?.roles || []

    // 检查权限
    const missingPermissions = requiredPermissions.filter(
      permission => !userPermissions.includes(permission)
    )

    // 检查角色
    const missingRoles = requiredRoles.filter(
      role => !userRoles.includes(role)
    )

    const hasPermission = missingPermissions.length === 0 && missingRoles.length === 0

    return {
      hasPermission,
      missingPermissions: missingPermissions.length > 0 ? missingPermissions : undefined,
      missingRoles: missingRoles.length > 0 ? missingRoles : undefined,
      redirectTo: !hasPermission ? '/403' : undefined
    }
  }

  /**
   * 更新面包屑
   */
  private static updateBreadcrumbs(route: RouteLocationNormalized): void {
    // 这里可以实现面包屑更新逻辑
    // 可以通过store或者事件总线来通知面包屑组件更新
    console.log('更新面包屑:', route.path)
  }

  /**
   * 添加到标签页
   */
  private static addToTabs(route: RouteLocationNormalized): void {
    // 这里可以实现标签页添加逻辑
    // 可以通过store来管理标签页状态
    if (!route.meta?.hideInMenu && route.meta?.title) {
      console.log('添加标签页:', route.meta.title)
    }
  }

  /**
   * 检查用户是否有指定权限
   */
  static hasPermission(permission: string): boolean {
    const userStore = useUserStore()
    return userStore.permissions.includes(permission)
  }

  /**
   * 检查用户是否有指定角色
   */
  static hasRole(role: string): boolean {
    const userStore = useUserStore()
    return userStore.roles.includes(role)
  }

  /**
   * 检查用户是否有任一权限
   */
  static hasAnyPermission(permissions: string[]): boolean {
    const userStore = useUserStore()
    return permissions.some(permission => userStore.permissions.includes(permission))
  }

  /**
   * 检查用户是否有任一角色
   */
  static hasAnyRole(roles: string[]): boolean {
    const userStore = useUserStore()
    return roles.some(role => userStore.roles.includes(role))
  }
}
