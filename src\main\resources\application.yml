# 主配置文件 - 通用配置
server:
  port: 8081
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}  # 默认使用开发环境，可通过环境变量覆盖

  # 应用信息
  application:
    name: cllcn-logistics-platform

  # Web配置
  web:
    resources:
      add-mappings: false  # 禁用默认静态资源处理，避免与API路径冲突

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# JWT配置
jwt:
  secret: ${JWT_SECRET:cllcn_logistics_platform_secret_key_2025}
  expiration: ${JWT_EXPIRATION:86400000}  # 24小时
  header: Authorization
  token-prefix: Bearer

# 自定义应用配置
app:
  upload:
    # 物理存储根路径
    path: ${UPLOAD_PATH:D:/Jobs/Project/cllcn_logistics_platform/uploads}
    # URL访问前缀
    url-prefix: ${FILE_ACCESS_URL_PREFIX:/uploads}
    # 应用基础URL
    base-url: ${APP_BASE_URL:http://localhost:8081}

  # 跨域配置
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3001,http://localhost:3002}
    allowed-methods: ${CORS_ALLOWED_METHODS:GET,POST,PUT,DELETE,OPTIONS}
    allowed-headers: ${CORS_ALLOWED_HEADERS:*}
    allow-credentials: true
    max-age: 3600

# 日志配置
logging:
  level:
    com.example.cllcnplatformbackend: INFO
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized