# 开发环境配置文件
server:
  port: 8081
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

spring:
  # 应用信息
  application:
    name: cllcn-logistics-platform

  # Web配置
  web:
    resources:
      add-mappings: false  # 禁用默认静态资源处理，避免与API路径冲突

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

  # 数据源配置 - 混合架构（用户认证共享，业务数据独立）
  datasource:
    # 公司管理系统数据库（用户认证共享）
    company:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: ********************************************************************************************************************************************************************
      username: root
      password: 123456
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000

    # 物流平台数据库（业务数据独立）
    logistics:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: *************************************************************************************************************************************************************
      username: root
      password: 123456
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update  # 开发环境使用update，自动更新表结构
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
        use_sql_comments: true
        # 启用统计信息用于监控
        generate_statistics: false

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 5000ms

  # SQL初始化
  sql:
    init:
      mode: never  # 开发环境不自动执行SQL脚本

# JWT配置
jwt:
  secret: cllcn_logistics_platform_dev_secret_key_2025
  expiration: 86400000  # 24小时
  header: Authorization
  token-prefix: Bearer

# 自定义应用配置
app:
  upload:
    # 物理存储根路径
    path: D:/Jobs/Project/cllcn_logistics_platform/uploads
    # URL访问前缀
    url-prefix: /uploads
    # 应用基础URL
    base-url: http://localhost:8081

  # 跨域配置
  cors:
    allowed-origins: http://localhost:3001,http://localhost:3002
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600

# 日志配置
logging:
  level:
    com.example.cllcnplatformbackend: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/cllcn-logistics-dev.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30

# SpringDoc配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    operations-sorter: method
    tags-sorter: alpha
  show-actuator: true

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 开发环境暴露所有端点
  endpoint:
    health:
      show-details: always
  health:
    rabbit:
      enabled: false  # 开发环境禁用RabbitMQ健康检查