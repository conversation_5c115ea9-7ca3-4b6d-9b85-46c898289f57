package com.example.cllcnplatformbackend.utils;

import java.util.Map;

/**
 * 用户上下文工具类
 * 基于ThreadLocalUtil封装，提供便捷的用户信息获取方法
 * 
 * <AUTHOR> Logistics Platform
 * @since 2025-07-31
 */
public class UserContext {
    
    /**
     * 获取当前用户ID
     * 
     * @return 用户ID（格式：E123 或 C456）
     */
    public static String getCurrentUserId() {
        Map<String, Object> userInfo = ThreadLocalUtil.get(Map.class);
        if (userInfo == null) {
            throw new RuntimeException("用户未登录");
        }
        
        Object userId = userInfo.get("userId");
        if (userId == null) {
            throw new RuntimeException("无法获取用户ID");
        }
        
        return userId.toString();
    }
    
    /**
     * 获取当前用户名
     * 
     * @return 用户名
     */
    public static String getCurrentUsername() {
        Map<String, Object> userInfo = ThreadLocalUtil.get(Map.class);
        if (userInfo == null) {
            return null;
        }
        
        Object username = userInfo.get("username");
        return username != null ? username.toString() : null;
    }
    
    /**
     * 获取当前用户角色
     * 
     * @return 用户角色
     */
    public static String getCurrentUserRole() {
        Map<String, Object> userInfo = ThreadLocalUtil.get(Map.class);
        if (userInfo == null) {
            return null;
        }
        
        Object role = userInfo.get("role");
        return role != null ? role.toString() : null;
    }
    
    /**
     * 获取当前用户类型
     * 
     * @return 用户类型（employee 或 client）
     */
    public static String getCurrentUserType() {
        Map<String, Object> userInfo = ThreadLocalUtil.get(Map.class);
        if (userInfo == null) {
            return null;
        }
        
        Object userType = userInfo.get("userType");
        return userType != null ? userType.toString() : null;
    }
    
    /**
     * 判断当前用户是否已登录
     * 
     * @return true表示已登录，false表示未登录
     */
    public static boolean isLoggedIn() {
        return ThreadLocalUtil.hasValue();
    }
}
